import React, { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Entity, ToolDetails, CourseDetails, AgencyDetails, ContentCreatorDetails, CommunityDetails, NewsletterDetails } from '@/types/entity';
import { Review } from '@/types/review';
import ReviewItem from './ReviewItem';
import {
  Star, ExternalLink, Tag as TagIcon, Layers as CategoryIcon, Briefcase as EntityTypeIcon,
  Zap, BookOpen, Users, FileText, Megaphone, Users2, Newspaper, Palette, Share2,
  Bookmark, Globe, Mail, Twitter, Github, Linkedin, MessageCircle, CheckCircle,
  MapPin, Calendar, TrendingUp, Sparkles
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator
} from '@/components/ui/breadcrumb';
import ReviewForm, { ReviewFormData } from './ReviewForm';
import { useAuth } from '@/contexts/AuthContext';
import { useBookmarkContext } from '@/contexts/BookmarkContext';
import { useRouter } from 'next/navigation';
import { useShare } from '@/hooks/useShare';

interface DetailedResourceViewProps {
  entity: Entity;
  reviews: Review[];
  onLoadMoreReviews: () => void;
  hasMoreReviews: boolean;
  isLoadingReviews: boolean;
  reviewsTotalCount?: number;
  onSubmitReview: (data: ReviewFormData) => Promise<void>;
  isSubmittingReview: boolean;
  reviewSubmissionError?: string | null;
  reviewSubmissionSuccess?: string | null;
}

// Helper to format field keys for display
const formatFieldKey = (key: string) => {
  return key
    .replace(/_/g, ' ')
    .replace(/\b\w/g, (char) => char.toUpperCase());
};

// Helper to render a list of strings (e.g., key features, prerequisites)
const renderStringList = (items?: string[], title?: string) => {
  if (!items || items.length === 0) return null;
  return (
    <div className="mt-2">
      {title && <p className="text-sm font-semibold text-gray-600 mb-1">{title}:</p>}
      <ul className="list-disc list-inside pl-1 space-y-0.5">
        {items.map((item, index) => (
          <li key={index} className="text-sm text-gray-600">{item}</li>
        ))}
      </ul>
    </div>
  );
};

const DetailedResourceView: React.FC<DetailedResourceViewProps> = ({
  entity,
  reviews,
  onLoadMoreReviews,
  hasMoreReviews,
  isLoadingReviews,
  reviewsTotalCount,
  onSubmitReview,
  isSubmittingReview,
  reviewSubmissionError,
  reviewSubmissionSuccess
}) => {
  const { session } = useAuth();
  const router = useRouter();
  const { isBookmarked, toggleBookmark } = useBookmarkContext();
  const { shareEntity, getSocialShareUrls } = useShare();
  const [isBookmarkLoading, setIsBookmarkLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');

  const fallbackImage = '/images/placeholder-logo.png';
  const entityName = entity?.name || 'Unnamed Entity';

  const handleBookmark = async () => {
    if (!session) {
      router.push('/login');
      return;
    }

    setIsBookmarkLoading(true);
    try {
      await toggleBookmark(entity.id);
    } catch (error) {
      console.error('Failed to toggle bookmark:', error);
      // You could show a toast notification here
    } finally {
      setIsBookmarkLoading(false);
    }
  };

  const handleShare = async () => {
    try {
      await shareEntity(entity);
    } catch (error) {
      console.error('Failed to share entity:', error);
      // You could show a toast notification here
    }
  };

  // Mock data for enhanced features
  const mockCompanyInfo = {
    foundedYear: 2020,
    teamSize: '11-50',
    funding: 'Series A',
    headquarters: 'San Francisco, CA'
  };

  const mockSocialLinks = {
    twitter: 'https://twitter.com/example',
    github: 'https://github.com/example',
    linkedin: 'https://linkedin.com/company/example'
  };

  const mockIntegrations = ['Slack', 'Discord', 'Zapier', 'API'];
  const mockSupportOptions = ['Email Support', 'Live Chat', 'Documentation', 'Community Forum'];

  const renderStars = (rating: number) => {
    const stars = [];
    if (typeof rating !== 'number') return <span className="text-gray-500 text-sm">Not rated</span>;
    for (let i = 1; i <= 5; i++) {
      stars.push(
        <Star
          key={i}
          className={`w-5 h-5 ${i <= rating ? 'text-yellow-400' : 'text-gray-300'}`}
          fill={i <= rating ? 'currentColor' : 'none'}
        />,
      );
    }
    return stars;
  };

  const renderEntitySpecificDetails = () => {
    if (!entity.details) return null;

    let detailsContent = null;
    const commonDetailItemClass = "text-sm text-gray-600 dark:text-gray-400";
    const commonDetailLabelClass = "font-semibold text-gray-700 dark:text-gray-300";
    let icon = <Palette className="w-5 h-5 mr-2 text-primary" />; // Default icon
    let detailsTitle = `${entity.entityType?.name || 'Additional'} Details`;

    switch (entity.entityType?.slug) {
      case 'tool':
        const toolDetails = entity.details as ToolDetails;
        icon = <Zap className="w-5 h-5 mr-2 text-primary" />;
        detailsTitle = "Tool Specifications";
        detailsContent = (
          <>
            {toolDetails.technical_level && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Technical Level:</span> {toolDetails.technical_level}</p>}
            {renderStringList(toolDetails.key_features, 'Key Features')}
            {renderStringList(toolDetails.use_cases, 'Use Cases')}
            {renderStringList(toolDetails.integrations, 'Integrations')}
            {toolDetails.pricing_model && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Pricing:</span> {toolDetails.pricing_model}</p>}
            {typeof toolDetails.api_available === 'boolean' && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>API Available:</span> {toolDetails.api_available ? 'Yes' : 'No'}</p>}
            {typeof toolDetails.self_hosted_option === 'boolean' && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Self-Hosted Option:</span> {toolDetails.self_hosted_option ? 'Yes' : 'No'}</p>}
          </>
        );
        break;
      case 'course':
        const courseDetails = entity.details as CourseDetails;
        icon = <BookOpen className="w-5 h-5 mr-2 text-primary" />;
        detailsTitle = "Course Information";
        detailsContent = (
          <>
            {courseDetails.instructor_name && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Instructor:</span> {courseDetails.instructor_name}</p>}
            {courseDetails.duration_text && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Duration:</span> {courseDetails.duration_text}</p>}
            {courseDetails.level && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Level:</span> {courseDetails.level}</p>}
            {renderStringList(courseDetails.prerequisites, 'Prerequisites')}
            {renderStringList(courseDetails.learning_outcomes, 'Learning Outcomes')}
            {courseDetails.language && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Language:</span> {courseDetails.language}</p>}
            {typeof courseDetails.certificate_available === 'boolean' && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Certificate:</span> {courseDetails.certificate_available ? 'Available' : 'Not Available'}</p>}
          </>
        );
        break;
      case 'agency':
        const agencyDetails = entity.details as AgencyDetails;
        icon = <EntityTypeIcon className="w-5 h-5 mr-2 text-primary" />;
        detailsTitle = "Agency Overview";
        detailsContent = (
          <>
            {renderStringList(agencyDetails.services_offered, 'Services Offered')}
            {renderStringList(agencyDetails.specializations, 'Specializations')}
            {agencyDetails.portfolio_url && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Portfolio:</span> <a href={agencyDetails.portfolio_url} target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">View Portfolio <ExternalLink className='inline w-3 h-3 ml-0.5' /></a></p>}
            {agencyDetails.team_size && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Team Size:</span> {agencyDetails.team_size}</p>}
            {agencyDetails.region_served && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Region Served:</span> {agencyDetails.region_served}</p>}
            {agencyDetails.contact_email && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Contact:</span> <a href={`mailto:${agencyDetails.contact_email}`} className="text-primary hover:underline">{agencyDetails.contact_email}</a></p>}
          </>
        );
        break;
      case 'content-creator':
        const creatorDetails = entity.details as ContentCreatorDetails;
        icon = <Megaphone className="w-5 h-5 mr-2 text-primary" />;
        detailsTitle = "Creator Profile";
        detailsContent = (
          <>
            {creatorDetails.platform && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Main Platform:</span> {creatorDetails.platform}</p>}
            {creatorDetails.platform_url && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Platform Link:</span> <a href={creatorDetails.platform_url} target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">View Platform <ExternalLink className='inline w-3 h-3 ml-0.5' /></a></p>}
            {creatorDetails.subscriber_count && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Subscribers/Followers:</span> {creatorDetails.subscriber_count}</p>}
            {renderStringList(creatorDetails.content_focus, 'Content Focus')}
            {creatorDetails.collaboration_email && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Collaborations:</span> <a href={`mailto:${creatorDetails.collaboration_email}`} className="text-primary hover:underline">{creatorDetails.collaboration_email}</a></p>}
            {creatorDetails.sample_work_links && creatorDetails.sample_work_links.length > 0 && (
                <div className="mt-2">
                    <p className={`${commonDetailLabelClass} mb-1`}>Sample Work:</p>
                    <ul className="list-disc list-inside pl-1 space-y-0.5">
                    {creatorDetails.sample_work_links.map((link, index) => (
                        <li key={index} className={commonDetailItemClass}>
                        <a href={link} target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">Sample Link {index + 1} <ExternalLink className='inline w-3 h-3 ml-0.5' /></a>
                        </li>
                    ))}
                    </ul>
                </div>
            )}
          </>
        );
        break;
      case 'community':
        const communityDetails = entity.details as CommunityDetails;
        icon = <Users2 className="w-5 h-5 mr-2 text-primary" />;
        detailsTitle = "Community Hub";
        detailsContent = (
          <>
            {communityDetails.platform_name && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Platform:</span> {communityDetails.platform_name}</p>}
            {communityDetails.platform_url && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Community Link:</span> <a href={communityDetails.platform_url} target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">Join Community <ExternalLink className='inline w-3 h-3 ml-0.5' /></a></p>}
            {communityDetails.member_count && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Members:</span> {communityDetails.member_count}</p>}
            {renderStringList(communityDetails.main_topics, 'Main Topics')}
            {communityDetails.moderator_info && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Moderator Info:</span> {communityDetails.moderator_info}</p>}
            {communityDetails.entry_requirements && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Entry Requirements:</span> {communityDetails.entry_requirements}</p>}
          </>
        );
        break;
      case 'newsletter':
        const newsletterDetails = entity.details as NewsletterDetails;
        icon = <Newspaper className="w-5 h-5 mr-2 text-primary" />;
        detailsTitle = "Newsletter Info";
        detailsContent = (
          <>
            {newsletterDetails.author_name && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Author:</span> {newsletterDetails.author_name}</p>}
            {newsletterDetails.publication_schedule && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Schedule:</span> {newsletterDetails.publication_schedule}</p>}
            {newsletterDetails.target_audience && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Target Audience:</span> {newsletterDetails.target_audience}</p>}
            {renderStringList(newsletterDetails.topics_covered, 'Topics Covered')}
            {newsletterDetails.archive_url && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Archive:</span> <a href={newsletterDetails.archive_url} target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">View Archive <ExternalLink className='inline w-3 h-3 ml-0.5' /></a></p>}
            {newsletterDetails.subscription_link && <p className={commonDetailItemClass}><span className={commonDetailLabelClass}>Subscribe:</span> <a href={newsletterDetails.subscription_link} target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">Subscribe Here <ExternalLink className='inline w-3 h-3 ml-0.5' /></a></p>}
          </>
        );
        break;
      default:
        if (Object.keys(entity.details).length > 0) {
            detailsTitle = `Additional Details`; // Keep a generic title if type is unknown but details exist
            detailsContent = (
                <>
                {Object.entries(entity.details).map(([key, value]) => (
                    <p key={key} className={commonDetailItemClass}>
                    <span className={commonDetailLabelClass}>{formatFieldKey(key)}:</span> {String(value)}
                    </p>
                ))}
                </>
            );
        }
        break;
    }

    if (!detailsContent) return null;

    return (
      <div className="mt-6 py-6 border-t border-gray-200 dark:border-gray-700">
        <h3 className="text-2xl font-semibold text-gray-800 dark:text-gray-200 mb-4 flex items-center">
          {icon} {detailsTitle} {/* Use dynamic title and icon */}
        </h3>
        <div className="space-y-3">{detailsContent}</div> {/* Added space-y-3 for better spacing within details */}
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Breadcrumb Navigation */}
      <div className="bg-white border-b border-gray-200">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <Breadcrumb>
            <BreadcrumbList>
              <BreadcrumbItem>
                <BreadcrumbLink asChild>
                  <Link href="/">Home</Link>
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbLink asChild>
                  <Link href="/browse">Browse</Link>
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbPage>{entityName}</BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
        </div>
      </div>

      {/* Hero Section */}
      <div className="bg-white border-b border-gray-200">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex flex-col lg:flex-row gap-8">
            {/* Logo and Basic Info */}
            <div className="flex flex-col sm:flex-row gap-6 lg:flex-1">
              <div className="relative w-24 h-24 sm:w-32 sm:h-32 flex-shrink-0 bg-gradient-to-br from-indigo-50 to-purple-50 rounded-2xl flex items-center justify-center p-4 shadow-sm">
                <Image
                  src={entity.logoUrl || fallbackImage}
                  alt={`${entityName} logo`}
                  width={128}
                  height={128}
                  className="rounded-xl object-contain"
                />
              </div>

              <div className="flex-1 min-w-0">
                <div className="flex items-start justify-between mb-4">
                  <div>
                    <h1 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-2">{entityName}</h1>
                    <p className="text-lg text-gray-600 mb-4">{entity.description}</p>
                  </div>
                </div>

                {/* Rating and Metadata */}
                <div className="flex flex-wrap items-center gap-4 mb-6">
                  <div className="flex items-center gap-2">
                    <div className="flex items-center">{renderStars(entity.avgRating)}</div>
                    <span className="text-sm font-medium text-gray-900">{entity.avgRating || 0}</span>
                    <span className="text-sm text-gray-500">({entity.reviewCount} reviews)</span>
                  </div>

                  <Badge variant="secondary" className="bg-indigo-100 text-indigo-700">
                    <EntityTypeIcon className="w-3 h-3 mr-1" />
                    {entity.entityType?.name || 'Tool'}
                  </Badge>

                  <Badge variant="outline">
                    <CategoryIcon className="w-3 h-3 mr-1" />
                    {entity.categories?.[0]?.name || 'AI'}
                  </Badge>

                  {entity.tags && entity.tags.slice(0, 2).map((tag) => (
                    <Badge key={tag.id} variant="outline" className="text-xs">
                      {tag.name}
                    </Badge>
                  ))}
                </div>

                {/* Action Buttons */}
                <div className="flex flex-wrap gap-3">
                  {entity.websiteUrl && (
                    <Button asChild size="lg" className="bg-indigo-600 hover:bg-indigo-700">
                      <Link href={entity.websiteUrl} target="_blank" rel="noopener noreferrer">
                        <Globe className="w-4 h-4 mr-2" />
                        Visit Website
                        <ExternalLink className="w-4 h-4 ml-2" />
                      </Link>
                    </Button>
                  )}

                  <Button
                    variant="outline"
                    size="lg"
                    onClick={handleBookmark}
                    disabled={isBookmarkLoading}
                  >
                    {isBookmarkLoading ? (
                      <div className="w-4 h-4 mr-2 border border-gray-300 border-t-gray-600 rounded-full animate-spin"></div>
                    ) : (
                      <Bookmark className={`w-4 h-4 mr-2 ${isBookmarked(entity.id) ? 'fill-current' : ''}`} />
                    )}
                    {isBookmarked(entity.id) ? 'Saved' : 'Save'}
                  </Button>

                  <Button variant="outline" size="lg" onClick={handleShare}>
                    <Share2 className="w-4 h-4 mr-2" />
                    Share
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Main Content Area */}
          <div className="lg:flex-1">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid w-full grid-cols-4 mb-8">
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="features">Features</TabsTrigger>
                <TabsTrigger value="pricing">Pricing</TabsTrigger>
                <TabsTrigger value="reviews">Reviews</TabsTrigger>
              </TabsList>

              {/* Overview Tab */}
              <TabsContent value="overview" className="space-y-8">
                <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                  <h2 className="text-2xl font-bold text-gray-900 mb-4">About {entityName}</h2>
                  <div className="prose max-w-none">
                    <p className="text-gray-600 leading-relaxed mb-6">
                      {entity.description || 'No description available.'}
                    </p>
                  </div>

                  {/* Entity Specific Details */}
                  {renderEntitySpecificDetails()}

                  {/* Use Cases */}
                  <div className="mt-8">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Use Cases</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {['Content Creation', 'Data Analysis', 'Automation', 'Research'].map((useCase, index) => (
                        <div key={index} className="flex items-center p-3 bg-gray-50 rounded-lg">
                          <CheckCircle className="w-5 h-5 text-green-500 mr-3" />
                          <span className="text-gray-700">{useCase}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Tags */}
                  {entity.tags && entity.tags.length > 0 && (
                    <div className="mt-8">
                      <h3 className="text-lg font-semibold text-gray-900 mb-4">Tags</h3>
                      <div className="flex flex-wrap gap-2">
                        {entity.tags.map((tag) => (
                          <Badge key={tag.id} variant="secondary" className="bg-gray-100 text-gray-700">
                            <TagIcon className="w-3 h-3 mr-1" />
                            {tag.name}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </TabsContent>

              {/* Features Tab */}
              <TabsContent value="features" className="space-y-6">
                <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                  <h2 className="text-2xl font-bold text-gray-900 mb-6">Key Features</h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {['AI-Powered Analysis', 'Real-time Processing', 'Cloud Integration', 'Advanced Analytics', 'Custom Workflows', 'API Access'].map((feature, index) => (
                      <div key={index} className="flex items-start p-4 border border-gray-200 rounded-lg hover:border-indigo-300 transition-colors">
                        <div className="flex-shrink-0 w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center mr-4">
                          <Sparkles className="w-4 h-4 text-indigo-600" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-gray-900 mb-1">{feature}</h3>
                          <p className="text-sm text-gray-600">Advanced feature description goes here.</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </TabsContent>

              {/* Pricing Tab */}
              <TabsContent value="pricing" className="space-y-6">
                <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                  <h2 className="text-2xl font-bold text-gray-900 mb-6">Pricing Plans</h2>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    {[
                      { name: 'Free', price: '$0', interval: 'forever', features: ['Basic features', '100 requests/month', 'Community support'] },
                      { name: 'Pro', price: '$29', interval: 'month', features: ['All features', '10,000 requests/month', 'Priority support', 'API access'], highlighted: true },
                      { name: 'Enterprise', price: 'Custom', interval: '', features: ['Unlimited requests', 'Custom integrations', 'Dedicated support', 'SLA'] }
                    ].map((plan, index) => (
                      <div key={index} className={`relative p-6 rounded-xl border-2 ${plan.highlighted ? 'border-indigo-500 bg-indigo-50' : 'border-gray-200'}`}>
                        {plan.highlighted && (
                          <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                            <Badge className="bg-indigo-600 text-white">Most Popular</Badge>
                          </div>
                        )}
                        <div className="text-center mb-6">
                          <h3 className="text-lg font-semibold text-gray-900 mb-2">{plan.name}</h3>
                          <div className="flex items-baseline justify-center">
                            <span className="text-3xl font-bold text-gray-900">{plan.price}</span>
                            {plan.interval && <span className="text-gray-500 ml-1">/{plan.interval}</span>}
                          </div>
                        </div>
                        <ul className="space-y-3 mb-6">
                          {plan.features.map((feature, featureIndex) => (
                            <li key={featureIndex} className="flex items-center">
                              <CheckCircle className="w-4 h-4 text-green-500 mr-3" />
                              <span className="text-gray-600">{feature}</span>
                            </li>
                          ))}
                        </ul>
                        <Button className={`w-full ${plan.highlighted ? 'bg-indigo-600 hover:bg-indigo-700' : ''}`} variant={plan.highlighted ? 'default' : 'outline'}>
                          Get Started
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              </TabsContent>

              {/* Reviews Tab */}
              <TabsContent value="reviews" className="space-y-6">
                <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                  <div className="flex items-center justify-between mb-6">
                    <h2 className="text-2xl font-bold text-gray-900">
                      User Reviews ({reviews?.length || 0}
                      {reviewsTotalCount && reviewsTotalCount > (reviews?.length || 0) ? ` of ${reviewsTotalCount}` : ''})
                    </h2>
                  </div>

                  {reviews && reviews.length > 0 ? (
                    <div className="space-y-6">
                      {reviews.map((review) => (
                        <ReviewItem key={review.id} review={review} />
                      ))}
                    </div>
                  ) : (
                    !isLoadingReviews && (
                      <div className="text-center py-12">
                        <MessageCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                        <p className="text-gray-600">No reviews yet for {entityName}. Be the first to share your thoughts!</p>
                      </div>
                    )
                  )}

                  {isLoadingReviews && (
                    <div className="text-center py-8">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto"></div>
                      <p className="text-gray-500 mt-2">Loading reviews...</p>
                    </div>
                  )}

                  {hasMoreReviews && !isLoadingReviews && (
                    <div className="mt-8 text-center">
                      <Button onClick={onLoadMoreReviews} variant="outline" size="lg">
                        Load More Reviews
                      </Button>
                    </div>
                  )}

                  {/* Write a Review Section */}
                  {session ? (
                    <div className="mt-8 pt-8 border-t border-gray-200">
                      <h3 className="text-lg font-semibold text-gray-900 mb-6">Write a Review for {entityName}</h3>
                      <ReviewForm
                        entityId={entity.id}
                        onSubmitReview={onSubmitReview}
                        isSubmitting={isSubmittingReview}
                        formError={reviewSubmissionError}
                        formSuccess={reviewSubmissionSuccess}
                      />
                    </div>
                  ) : (
                    <div className="mt-8 pt-8 border-t border-gray-200 text-center">
                      <p className="text-gray-600">
                        Please <Link href="/login" className="text-indigo-600 hover:text-indigo-700 font-medium">log in</Link> to write a review.
                      </p>
                    </div>
                  )}
                </div>
              </TabsContent>
            </Tabs>
          </div>

          {/* Sidebar */}
          <div className="lg:w-80 space-y-6">
            {/* Quick Actions */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
              <div className="space-y-3">
                {entity.websiteUrl && (
                  <Button asChild className="w-full bg-indigo-600 hover:bg-indigo-700">
                    <Link href={entity.websiteUrl} target="_blank" rel="noopener noreferrer">
                      <Globe className="w-4 h-4 mr-2" />
                      Visit Website
                    </Link>
                  </Button>
                )}
                <Button variant="outline" className="w-full">
                  <Mail className="w-4 h-4 mr-2" />
                  Contact Support
                </Button>
                <Button variant="outline" className="w-full">
                  <FileText className="w-4 h-4 mr-2" />
                  Documentation
                </Button>
              </div>
            </div>

            {/* Company Info */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Company Info</h3>
              <div className="space-y-3">
                <div className="flex items-center text-sm">
                  <Calendar className="w-4 h-4 text-gray-400 mr-3" />
                  <span className="text-gray-600">Founded {mockCompanyInfo.foundedYear}</span>
                </div>
                <div className="flex items-center text-sm">
                  <Users className="w-4 h-4 text-gray-400 mr-3" />
                  <span className="text-gray-600">{mockCompanyInfo.teamSize} employees</span>
                </div>
                <div className="flex items-center text-sm">
                  <TrendingUp className="w-4 h-4 text-gray-400 mr-3" />
                  <span className="text-gray-600">{mockCompanyInfo.funding} funding</span>
                </div>
                <div className="flex items-center text-sm">
                  <MapPin className="w-4 h-4 text-gray-400 mr-3" />
                  <span className="text-gray-600">{mockCompanyInfo.headquarters}</span>
                </div>
              </div>
            </div>

            {/* Social Links */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Connect</h3>
              <div className="flex space-x-3">
                <Button variant="outline" size="sm" asChild>
                  <Link href={mockSocialLinks.twitter} target="_blank" rel="noopener noreferrer">
                    <Twitter className="w-4 h-4" />
                  </Link>
                </Button>
                <Button variant="outline" size="sm" asChild>
                  <Link href={mockSocialLinks.github} target="_blank" rel="noopener noreferrer">
                    <Github className="w-4 h-4" />
                  </Link>
                </Button>
                <Button variant="outline" size="sm" asChild>
                  <Link href={mockSocialLinks.linkedin} target="_blank" rel="noopener noreferrer">
                    <Linkedin className="w-4 h-4" />
                  </Link>
                </Button>
              </div>
            </div>

            {/* Integrations */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Integrations</h3>
              <div className="flex flex-wrap gap-2">
                {mockIntegrations.map((integration, index) => (
                  <Badge key={index} variant="secondary" className="bg-gray-100 text-gray-700">
                    {integration}
                  </Badge>
                ))}
              </div>
            </div>

            {/* Support Options */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Support</h3>
              <div className="space-y-2">
                {mockSupportOptions.map((option, index) => (
                  <div key={index} className="flex items-center text-sm">
                    <CheckCircle className="w-4 h-4 text-green-500 mr-3" />
                    <span className="text-gray-600">{option}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DetailedResourceView; 