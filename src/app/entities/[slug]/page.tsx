'use client';

import React, { useEffect, useState, useCallback } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { getEntityBySlug, getReviewsByEntityId, submitReview } from '@/services/api';
import { Entity, PaginationMeta } from '@/types/entity';
import { Review } from '@/types/review';
import DetailedResourceView from '@/components/resource/DetailedResourceView';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { ReviewFormData } from '@/components/resource/ReviewForm';
import { Metadata } from 'next';
import Head from 'next/head';

const REVIEWS_PAGE_SIZE = 5; // Number of reviews to fetch per page/load more click

// Generate metadata for SEO
export async function generateMetadata({ params }: { params: { slug: string } }): Promise<Metadata> {
  try {
    const entity = await getEntityBySlug(params.slug);

    const title = entity.metaTitle || `${entity.name} | AI Navigator`;
    const description = entity.metaDescription || entity.shortDescription || entity.description || `Discover ${entity.name} on AI Navigator`;
    const url = `${process.env.NEXT_PUBLIC_SITE_URL || 'https://ai-navigator.com'}/entities/${entity.slug}`;
    const imageUrl = entity.logoUrl || `${process.env.NEXT_PUBLIC_SITE_URL || 'https://ai-navigator.com'}/images/og-default.png`;

    return {
      title,
      description,
      keywords: [
        entity.name,
        entity.entityType.name,
        ...entity.categories.map(cat => cat.name),
        ...entity.tags.map(tag => tag.name),
        ...entity.features.map(feature => feature.name),
        'AI', 'artificial intelligence', 'tools', 'resources'
      ].join(', '),
      authors: [{ name: 'AI Navigator' }],
      creator: 'AI Navigator',
      publisher: 'AI Navigator',
      robots: {
        index: entity.status === 'ACTIVE',
        follow: true,
        googleBot: {
          index: entity.status === 'ACTIVE',
          follow: true,
        },
      },
      openGraph: {
        type: 'website',
        url,
        title,
        description,
        siteName: 'AI Navigator',
        images: [
          {
            url: imageUrl,
            width: 1200,
            height: 630,
            alt: `${entity.name} logo`,
          },
        ],
      },
      twitter: {
        card: 'summary_large_image',
        title,
        description,
        images: [imageUrl],
        creator: '@ai_navigator',
      },
      alternates: {
        canonical: url,
      },
    };
  } catch (error) {
    console.error('Error generating metadata:', error);
    return {
      title: 'Entity Not Found | AI Navigator',
      description: 'The requested entity could not be found.',
    };
  }
}

export default function EntitySlugPage() {
  const params = useParams();
  const router = useRouter();
  const entitySlug = params?.slug as string;

  const [entity, setEntity] = useState<Entity | null>(null);
  const [reviews, setReviews] = useState<Review[]>([]);
  const [reviewsPaginationMeta, setReviewsPaginationMeta] = useState<PaginationMeta | null>(null);
  const [isLoadingEntity, setIsLoadingEntity] = useState(true);
  const [isLoadingReviews, setIsLoadingReviews] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isSubmittingReview, setIsSubmittingReview] = useState(false);
  const [reviewSubmissionError, setReviewSubmissionError] = useState<string | null>(null);
  const [reviewSubmissionSuccess, setReviewSubmissionSuccess] = useState<string | null>(null);

  const { session } = useAuth();

  const fetchEntityData = useCallback(async () => {
    if (!entitySlug) {
      setError('Entity slug not found in URL.');
      setIsLoadingEntity(false);
      return;
    }
    setIsLoadingEntity(true);
    setError(null);
    try {
      const entityData = await getEntityBySlug(entitySlug, session?.access_token);
      setEntity(entityData);
    } catch (err: unknown) {
      console.error(`Failed to fetch entity ${entitySlug}:`, err);
      const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred while fetching entity details.';
      setError(errorMessage);
    }
    setIsLoadingEntity(false);
  }, [entitySlug, session]);

  const fetchReviews = useCallback(async (page: number = 1, isLoadMore: boolean = false) => {
    if (!entity?.id) return;

    setIsLoadingReviews(true);
    try {
      const reviewsResponse = await getReviewsByEntityId(
        entity.id,
        session?.access_token,
        page,
        REVIEWS_PAGE_SIZE
      );
      
      setReviews(prevReviews => 
        isLoadMore ? [...prevReviews, ...reviewsResponse.data] : reviewsResponse.data
      );
      setReviewsPaginationMeta(reviewsResponse.meta);
    } catch (err: unknown) {
      console.error(`Failed to fetch reviews for entity ${entity.id}:`, err);
      // Don't set error state for reviews failure, just log it
    }
    setIsLoadingReviews(false);
  }, [entity?.id, session]);

  const handleLoadMoreReviews = useCallback(() => {
    if (reviewsPaginationMeta?.hasNextPage) {
      fetchReviews(reviewsPaginationMeta.page + 1, true);
    }
  }, [reviewsPaginationMeta, fetchReviews]);

  const handleSubmitReview = useCallback(async (data: ReviewFormData) => {
    if (!entity?.id || !session?.access_token) {
      setReviewSubmissionError('You must be logged in to submit a review.');
      return;
    }

    setIsSubmittingReview(true);
    setReviewSubmissionError(null);
    setReviewSubmissionSuccess(null);

    try {
      await submitReview({
        entityId: entity.id,
        rating: data.rating,
        title: data.title,
        text: data.reviewText,
      }, session.access_token);

      setReviewSubmissionSuccess('Review submitted successfully!');
      // Refresh reviews after successful submission
      fetchReviews(1, false);
    } catch (err: unknown) {
      console.error('Failed to submit review:', err);
      const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred while submitting your review.';
      setReviewSubmissionError(errorMessage);
    }
    setIsSubmittingReview(false);
  }, [entity?.id, session, fetchReviews]);

  // Generate structured data for SEO
  const generateStructuredData = useCallback(() => {
    if (!entity) return null;

    const structuredData = {
      "@context": "https://schema.org",
      "@type": "SoftwareApplication",
      "name": entity.name,
      "description": entity.description,
      "url": entity.websiteUrl,
      "image": entity.logoUrl,
      "applicationCategory": entity.entityType.name,
      "operatingSystem": "Web",
      "offers": {
        "@type": "Offer",
        "price": entity.hasFreeTier ? "0" : "varies",
        "priceCurrency": "USD",
        "availability": "https://schema.org/InStock"
      },
      "aggregateRating": entity.reviewCount > 0 ? {
        "@type": "AggregateRating",
        "ratingValue": entity.avgRating,
        "reviewCount": entity.reviewCount,
        "bestRating": 5,
        "worstRating": 1
      } : undefined,
      "author": {
        "@type": "Organization",
        "name": entity.submitter?.user_metadata?.display_name || "Unknown"
      },
      "datePublished": entity.createdAt,
      "dateModified": entity.updatedAt,
      "keywords": [
        ...entity.categories.map(cat => cat.name),
        ...entity.tags.map(tag => tag.name),
        ...entity.features.map(feature => feature.name)
      ].join(", ")
    };

    return JSON.stringify(structuredData);
  }, [entity]);

  useEffect(() => {
    fetchEntityData();
  }, [fetchEntityData]);

  useEffect(() => {
    if (entity?.id) {
      fetchReviews();
    }
  }, [entity?.id, fetchReviews]);

  if (isLoadingEntity) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-2 border-indigo-200 border-t-indigo-600 rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading entity details...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center max-w-md mx-auto px-4">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Entity Not Found</h1>
          <p className="text-gray-600 mb-6">{error}</p>
          <Button onClick={() => router.push('/browse')} variant="outline">
            Browse All Entities
          </Button>
        </div>
      </div>
    );
  }

  if (!entity) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center max-w-md mx-auto px-4">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Entity Not Found</h1>
          <p className="text-gray-600 mb-6">The entity you're looking for doesn't exist or has been removed.</p>
          <Button onClick={() => router.push('/browse')} variant="outline">
            Browse All Entities
          </Button>
        </div>
      </div>
    );
  }

  const structuredData = generateStructuredData();

  return (
    <>
      {structuredData && (
        <Head>
          <script
            type="application/ld+json"
            dangerouslySetInnerHTML={{ __html: structuredData }}
          />
        </Head>
      )}
      <DetailedResourceView
      entity={entity}
      reviews={reviews}
      onLoadMoreReviews={handleLoadMoreReviews}
      hasMoreReviews={reviewsPaginationMeta?.hasNextPage || false}
      isLoadingReviews={isLoadingReviews}
      reviewsTotalCount={reviewsPaginationMeta?.totalItems}
      onSubmitReview={handleSubmitReview}
      isSubmittingReview={isSubmittingReview}
      reviewSubmissionError={reviewSubmissionError}
      reviewSubmissionSuccess={reviewSubmissionSuccess}
    />
    </>
  );
}
