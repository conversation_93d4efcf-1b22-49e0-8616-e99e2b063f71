'use client';

import React, { useEffect, useState, useCallback } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { getEntityBySlug, getReviewsByEntityId, submitReview } from '@/services/api';
import { Entity, PaginationMeta } from '@/types/entity';
import { Review } from '@/types/review';
import DetailedResourceView from '@/components/resource/DetailedResourceView';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { ReviewFormData } from '@/components/resource/ReviewForm';
import { Metadata } from 'next';
import Head from 'next/head';
import { applyEntityFallbacks } from '@/utils/entityFallbacks';
import { parseError, getErrorMessage, isRetryableError, logError } from '@/utils/errorHandling';
import { PageLoading, DetailLoading } from '@/components/loading/LoadingStates';
import ErrorBoundary from '@/components/error/ErrorBoundary';

const REVIEWS_PAGE_SIZE = 5; // Number of reviews to fetch per page/load more click

// Generate metadata for SEO
export async function generateMetadata({ params }: { params: { slug: string } }): Promise<Metadata> {
  try {
    const entity = await getEntityBySlug(params.slug);

    const title = entity.metaTitle || `${entity.name} | AI Navigator`;
    const description = entity.metaDescription || entity.shortDescription || entity.description || `Discover ${entity.name} on AI Navigator`;
    const url = `${process.env.NEXT_PUBLIC_SITE_URL || 'https://ai-navigator.com'}/entities/${entity.slug}`;
    const imageUrl = entity.logoUrl || `${process.env.NEXT_PUBLIC_SITE_URL || 'https://ai-navigator.com'}/images/og-default.png`;

    return {
      title,
      description,
      keywords: [
        entity.name,
        entity.entityType.name,
        ...entity.categories.map(cat => cat.name),
        ...entity.tags.map(tag => tag.name),
        ...entity.features.map(feature => feature.name),
        'AI', 'artificial intelligence', 'tools', 'resources'
      ].join(', '),
      authors: [{ name: 'AI Navigator' }],
      creator: 'AI Navigator',
      publisher: 'AI Navigator',
      robots: {
        index: entity.status === 'ACTIVE',
        follow: true,
        googleBot: {
          index: entity.status === 'ACTIVE',
          follow: true,
        },
      },
      openGraph: {
        type: 'website',
        url,
        title,
        description,
        siteName: 'AI Navigator',
        images: [
          {
            url: imageUrl,
            width: 1200,
            height: 630,
            alt: `${entity.name} logo`,
          },
        ],
      },
      twitter: {
        card: 'summary_large_image',
        title,
        description,
        images: [imageUrl],
        creator: '@ai_navigator',
      },
      alternates: {
        canonical: url,
      },
    };
  } catch (error) {
    console.error('Error generating metadata:', error);
    return {
      title: 'Entity Not Found | AI Navigator',
      description: 'The requested entity could not be found.',
    };
  }
}

export default function EntitySlugPage() {
  const params = useParams();
  const router = useRouter();
  const entitySlug = params?.slug as string;

  const [entity, setEntity] = useState<Entity | null>(null);
  const [reviews, setReviews] = useState<Review[]>([]);
  const [reviewsPaginationMeta, setReviewsPaginationMeta] = useState<PaginationMeta | null>(null);
  const [isLoadingEntity, setIsLoadingEntity] = useState(true);
  const [isLoadingReviews, setIsLoadingReviews] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const [isSubmittingReview, setIsSubmittingReview] = useState(false);
  const [reviewSubmissionError, setReviewSubmissionError] = useState<string | null>(null);
  const [reviewSubmissionSuccess, setReviewSubmissionSuccess] = useState<string | null>(null);

  const { session } = useAuth();

  const fetchEntityData = useCallback(async () => {
    if (!entitySlug) {
      setError('Entity slug not found in URL.');
      setIsLoadingEntity(false);
      return;
    }
    setIsLoadingEntity(true);
    setError(null);
    try {
      const entityData = await getEntityBySlug(entitySlug, session?.access_token);
      const enhancedEntity = applyEntityFallbacks(entityData);
      setEntity(enhancedEntity);
    } catch (err: unknown) {
      logError(err, `Failed to fetch entity with slug: ${entitySlug}`);
      const errorMessage = getErrorMessage(err);
      setError(errorMessage);
    }
    setIsLoadingEntity(false);
  }, [entitySlug, session]);

  const fetchReviews = useCallback(async (page: number = 1, isLoadMore: boolean = false) => {
    if (!entity?.id) return;

    setIsLoadingReviews(true);
    try {
      const reviewsResponse = await getReviewsByEntityId(
        entity.id,
        session?.access_token,
        page,
        REVIEWS_PAGE_SIZE
      );
      
      setReviews(prevReviews => 
        isLoadMore ? [...prevReviews, ...reviewsResponse.data] : reviewsResponse.data
      );
      setReviewsPaginationMeta(reviewsResponse.meta);
    } catch (err: unknown) {
      logError(err, `Failed to fetch reviews for entity ${entity.id}`);
      // Don't set error state for reviews failure, just log it
    }
    setIsLoadingReviews(false);
  }, [entity?.id, session]);

  const handleLoadMoreReviews = useCallback(() => {
    if (reviewsPaginationMeta?.hasNextPage) {
      fetchReviews(reviewsPaginationMeta.page + 1, true);
    }
  }, [reviewsPaginationMeta, fetchReviews]);

  const handleSubmitReview = useCallback(async (data: ReviewFormData) => {
    if (!entity?.id || !session?.access_token) {
      setReviewSubmissionError('You must be logged in to submit a review.');
      return;
    }

    setIsSubmittingReview(true);
    setReviewSubmissionError(null);
    setReviewSubmissionSuccess(null);

    try {
      await submitReview({
        entityId: entity.id,
        rating: data.rating,
        title: data.title,
        text: data.reviewText,
      }, session.access_token);

      setReviewSubmissionSuccess('Review submitted successfully!');
      // Refresh reviews after successful submission
      fetchReviews(1, false);
    } catch (err: unknown) {
      logError(err, 'Failed to submit review');
      const errorMessage = getErrorMessage(err);
      setReviewSubmissionError(errorMessage);
    }
    setIsSubmittingReview(false);
  }, [entity?.id, session, fetchReviews]);

  const handleRetry = useCallback(() => {
    setRetryCount(prev => prev + 1);
    setError(null);
    fetchEntityData();
  }, [fetchEntityData]);

  // Generate structured data for SEO
  const generateStructuredData = useCallback(() => {
    if (!entity) return null;

    const structuredData = {
      "@context": "https://schema.org",
      "@type": "SoftwareApplication",
      "name": entity.name,
      "description": entity.description,
      "url": entity.websiteUrl,
      "image": entity.logoUrl,
      "applicationCategory": entity.entityType.name,
      "operatingSystem": "Web",
      "offers": {
        "@type": "Offer",
        "price": entity.hasFreeTier ? "0" : "varies",
        "priceCurrency": "USD",
        "availability": "https://schema.org/InStock"
      },
      "aggregateRating": entity.reviewCount > 0 ? {
        "@type": "AggregateRating",
        "ratingValue": entity.avgRating,
        "reviewCount": entity.reviewCount,
        "bestRating": 5,
        "worstRating": 1
      } : undefined,
      "author": {
        "@type": "Organization",
        "name": entity.submitter?.user_metadata?.display_name || "Unknown"
      },
      "datePublished": entity.createdAt,
      "dateModified": entity.updatedAt,
      "keywords": [
        ...entity.categories.map(cat => cat.name),
        ...entity.tags.map(tag => tag.name),
        ...entity.features.map(feature => feature.name)
      ].join(", ")
    };

    return JSON.stringify(structuredData);
  }, [entity]);

  useEffect(() => {
    fetchEntityData();
  }, [fetchEntityData]);

  useEffect(() => {
    if (entity?.id) {
      fetchReviews();
    }
  }, [entity?.id, fetchReviews]);

  if (isLoadingEntity) {
    return <PageLoading message="Loading entity details..." />;
  }

  if (error) {
    const parsedError = parseError(error);
    const canRetry = isRetryableError(error);

    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center max-w-md mx-auto px-4">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            {parsedError.status === 404 ? 'Entity Not Found' : 'Something went wrong'}
          </h1>
          <p className="text-gray-600 mb-6">{error}</p>
          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            {canRetry && (
              <Button onClick={handleRetry} variant="outline">
                Try Again
              </Button>
            )}
            <Button onClick={() => router.push('/browse')}>
              Browse All Entities
            </Button>
          </div>
        </div>
      </div>
    );
  }

  if (!entity) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center max-w-md mx-auto px-4">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Entity Not Found</h1>
          <p className="text-gray-600 mb-6">The entity you're looking for doesn't exist or has been removed.</p>
          <Button onClick={() => router.push('/browse')} variant="outline">
            Browse All Entities
          </Button>
        </div>
      </div>
    );
  }

  const structuredData = generateStructuredData();

  return (
    <ErrorBoundary>
      {structuredData && (
        <Head>
          <script
            type="application/ld+json"
            dangerouslySetInnerHTML={{ __html: structuredData }}
          />
        </Head>
      )}
      <DetailedResourceView
      entity={entity}
      reviews={reviews}
      onLoadMoreReviews={handleLoadMoreReviews}
      hasMoreReviews={reviewsPaginationMeta?.hasNextPage || false}
      isLoadingReviews={isLoadingReviews}
      reviewsTotalCount={reviewsPaginationMeta?.totalItems}
      onSubmitReview={handleSubmitReview}
      isSubmittingReview={isSubmittingReview}
      reviewSubmissionError={reviewSubmissionError}
      reviewSubmissionSuccess={reviewSubmissionSuccess}
    />
    </ErrorBoundary>
  );
}
